/**
 * 用户档位管理工具类
 * 负责管理用户偏好档位的存储、恢复和同步
 */

import { sendData, decimalToHexadecimal } from "../pages/LB1001Setting/index";
import toast from "../pages/LB1001Setting/toast";

export interface GearRestoreOptions {
  maxRetries?: number;
  retryDelay?: number;
  verifyDelay?: number;
  showToast?: boolean;
}

export interface GearRestoreResult {
  success: boolean;
  finalGear: number;
  attempts: number;
  error?: string;
}

export class UserGearManager {
  private deviceSN: string;
  private currentGear: number = 1;
  private preferredGear: number = 1;
  private restoreAttempts: number = 0;
  private maxRetries: number = 3;
  private isRestoring: boolean = false;
  private restoreTimer: any = null;

  constructor(deviceSN: string) {
    this.deviceSN = deviceSN;
    this.loadPreferredGear();
  }

  /**
   * 从本地存储加载用户偏好档位
   */
  private loadPreferredGear(): void {
    if (!this.deviceSN) return;
    
    const deviceKey = `userGear_${this.deviceSN}`;
    const savedGear = wx.getStorageSync(deviceKey);
    
    if (savedGear && savedGear >= 1 && savedGear <= 15) {
      this.preferredGear = savedGear;
      console.log(`加载用户偏好档位: ${this.preferredGear} (设备: ${this.deviceSN})`);
    } else {
      this.preferredGear = 1; // 默认档位
      console.log(`使用默认档位: ${this.preferredGear} (设备: ${this.deviceSN})`);
    }
  }

  /**
   * 保存用户偏好档位到本地存储
   * @param gear 档位值 (1-15)
   */
  savePreferredGear(gear: number): boolean {
    if (!this.deviceSN || gear < 1 || gear > 15) {
      console.warn(`无效的档位值或设备SN: gear=${gear}, deviceSN=${this.deviceSN}`);
      return false;
    }

    const deviceKey = `userGear_${this.deviceSN}`;
    try {
      wx.setStorageSync(deviceKey, gear);
      this.preferredGear = gear;
      console.log(`保存用户偏好档位: ${gear} (设备: ${this.deviceSN})`);
      return true;
    } catch (error) {
      console.error("保存用户偏好档位失败:", error);
      return false;
    }
  }

  /**
   * 更新设备当前档位
   * @param gear 当前档位
   */
  updateCurrentGear(gear: number): void {
    if (gear >= 1 && gear <= 15) {
      this.currentGear = gear;
      console.log(`更新设备当前档位: ${gear}`);
    }
  }

  /**
   * 获取用户偏好档位
   */
  getPreferredGear(): number {
    return this.preferredGear;
  }

  /**
   * 获取设备当前档位
   */
  getCurrentGear(): number {
    return this.currentGear;
  }

  /**
   * 检查是否需要恢复档位
   */
  needsRestore(): boolean {
    return this.currentGear !== this.preferredGear && this.preferredGear > 0;
  }

  /**
   * 恢复用户档位（带重试机制）
   * @param options 恢复选项
   * @returns Promise<GearRestoreResult>
   */
  async restoreUserGear(options: GearRestoreOptions = {}): Promise<GearRestoreResult> {
    const {
      maxRetries = 3,
      retryDelay = 2000,
      verifyDelay = 3000,
      showToast = true
    } = options;

    if (this.isRestoring) {
      console.log("档位恢复正在进行中，跳过重复请求");
      return {
        success: false,
        finalGear: this.currentGear,
        attempts: this.restoreAttempts,
        error: "恢复正在进行中"
      };
    }

    if (!this.needsRestore()) {
      console.log("档位一致，无需恢复");
      return {
        success: true,
        finalGear: this.currentGear,
        attempts: 0
      };
    }

    this.isRestoring = true;
    this.restoreAttempts = 0;
    this.maxRetries = maxRetries;

    console.log(`开始恢复用户档位: ${this.preferredGear} (当前: ${this.currentGear})`);

    try {
      const result = await this.performRestore(retryDelay, verifyDelay, showToast);
      return result;
    } finally {
      this.isRestoring = false;
      this.clearRestoreTimer();
    }
  }

  /**
   * 执行档位恢复
   */
  private async performRestore(
    retryDelay: number,
    verifyDelay: number,
    showToast: boolean
  ): Promise<GearRestoreResult> {
    while (this.restoreAttempts < this.maxRetries) {
      this.restoreAttempts++;
      
      try {
        console.log(`第${this.restoreAttempts}次尝试恢复档位到: ${this.preferredGear}`);
        
        // 发送档位设置指令
        const distance = decimalToHexadecimal(this.preferredGear);
        const data = "FFCC0100" + distance + "BBAA";
        await sendData(data);

        // 等待设备响应
        const verifyResult = await this.waitForVerification(verifyDelay);
        
        if (verifyResult.success) {
          if (showToast) {
            toast.info(`已恢复到用户档位: ${this.preferredGear}`);
          }
          console.log(`档位恢复成功: ${this.preferredGear}`);
          return {
            success: true,
            finalGear: this.preferredGear,
            attempts: this.restoreAttempts
          };
        }

        // 如果不是最后一次尝试，等待后重试
        if (this.restoreAttempts < this.maxRetries) {
          console.log(`档位恢复验证失败，${retryDelay}ms后重试`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }

      } catch (error) {
        console.error(`第${this.restoreAttempts}次档位恢复失败:`, error);
        
        // 如果不是最后一次尝试，等待后重试
        if (this.restoreAttempts < this.maxRetries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }

    // 所有尝试都失败了
    const errorMsg = "档位恢复失败，请手动调整";
    if (showToast) {
      toast.fail(errorMsg);
    }
    console.log(`档位恢复达到最大尝试次数(${this.maxRetries})，停止恢复`);
    
    return {
      success: false,
      finalGear: this.currentGear,
      attempts: this.restoreAttempts,
      error: errorMsg
    };
  }

  /**
   * 等待档位验证
   */
  private waitForVerification(delay: number): Promise<{ success: boolean }> {
    return new Promise((resolve) => {
      this.restoreTimer = setTimeout(() => {
        const success = this.currentGear === this.preferredGear;
        resolve({ success });
      }, delay);
    });
  }

  /**
   * 清除恢复定时器
   */
  private clearRestoreTimer(): void {
    if (this.restoreTimer) {
      clearTimeout(this.restoreTimer);
      this.restoreTimer = null;
    }
  }

  /**
   * 停止档位恢复
   */
  stopRestore(): void {
    this.isRestoring = false;
    this.clearRestoreTimer();
    console.log("档位恢复已停止");
  }

  /**
   * 重置状态
   */
  reset(): void {
    this.stopRestore();
    this.restoreAttempts = 0;
    this.currentGear = 1;
    console.log("用户档位管理器已重置");
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      deviceSN: this.deviceSN,
      currentGear: this.currentGear,
      preferredGear: this.preferredGear,
      needsRestore: this.needsRestore(),
      isRestoring: this.isRestoring,
      restoreAttempts: this.restoreAttempts,
      maxRetries: this.maxRetries
    };
  }
}

/**
 * 创建用户档位管理器实例
 * @param deviceSN 设备序列号
 * @returns UserGearManager实例
 */
export function createUserGearManager(deviceSN: string): UserGearManager {
  return new UserGearManager(deviceSN);
}

export default UserGearManager;
