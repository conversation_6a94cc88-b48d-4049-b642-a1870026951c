# 用户档位管理优化方案

## 问题分析

### 原始问题
页面退出时恢复用户档位可能出现设备端没有响应的情况，导致用户档位丢失或不一致。

### 问题根因
1. **单次恢复无重试机制**：页面退出时只发送一次恢复指令，如果设备无响应则失败
2. **缺乏验证机制**：无法确认设备是否成功接收并执行了档位恢复指令
3. **状态管理混乱**：用户档位、设备档位、临时档位状态管理不清晰
4. **时机不当**：在页面退出的关键时刻进行档位恢复，容易被中断

## 优化方案

### 1. 核心思路
- **提前恢复**：在页面进入时检查并恢复用户档位，而不是等到退出时
- **重试机制**：带有重试机制的档位恢复，确保成功率
- **状态分离**：清晰区分用户偏好档位和设备当前档位
- **专业管理**：使用专门的档位管理器统一处理档位相关逻辑

### 2. 技术实现

#### 2.1 用户档位管理器 (`UserGearManager`)
```typescript
// 位置: miniprogram/utils/userGearManager.ts
export class UserGearManager {
  // 核心功能
  - savePreferredGear(gear: number): 保存用户偏好档位
  - updateCurrentGear(gear: number): 更新设备当前档位
  - needsRestore(): 检查是否需要恢复
  - restoreUserGear(options): 带重试机制的档位恢复
  
  // 特性
  - 自动重试（默认3次）
  - 验证机制（确认恢复成功）
  - 状态管理（区分偏好档位和当前档位）
  - 错误处理（超时、失败处理）
}
```

#### 2.2 页面集成改造
```typescript
// 新增数据字段
data: {
  userPreferredGear: 0,     // 用户偏好档位
  deviceCurrentGear: 0,     // 设备当前档位
  gearRestoreAttempts: 0,   // 档位恢复尝试次数
  maxRestoreAttempts: 3,    // 最大恢复尝试次数
  gearRestoreTimer: null,   // 档位恢复定时器
  needGearRestore: false,   // 是否需要档位恢复
}

// 关键方法
- initUserGearManager(): 初始化档位管理器
- saveUserPreferredGear(): 保存用户偏好档位
- updateDeviceCurrentGear(): 更新设备当前档位
- checkAndRestoreUserGear(): 检查并恢复用户档位
```

### 3. 工作流程

#### 3.1 页面进入流程
```
1. 页面onShow() 
   ↓
2. 初始化蓝牙连接
   ↓
3. 校验密码并查询设备状态
   ↓
4. 初始化用户档位管理器
   ↓
5. 启动定时器
   ↓
6. 延迟2秒后检查并恢复用户档位
```

#### 3.2 档位恢复流程
```
1. 检查是否需要恢复
   ↓
2. 发送档位设置指令
   ↓
3. 等待3秒验证结果
   ↓
4. 验证成功 → 完成恢复
   ↓
5. 验证失败 → 重试（最多3次）
   ↓
6. 超过重试次数 → 提示用户手动调整
```

#### 3.3 用户操作流程
```
1. 用户手动设置档位/智能调距
   ↓
2. 保存为用户偏好档位
   ↓
3. 发送设置指令到设备
   ↓
4. 设备响应后更新当前档位
```

### 4. 关键优化点

#### 4.1 时机优化
- **从退出时恢复改为进入时恢复**：避免页面切换时的中断风险
- **延迟恢复**：等待设备状态稳定后再进行档位检查

#### 4.2 可靠性优化
- **重试机制**：最多3次重试，每次间隔2秒
- **验证机制**：发送指令后等待3秒验证结果
- **超时保护**：避免无限等待

#### 4.3 用户体验优化
- **智能检测**：只在档位不一致时才进行恢复
- **状态提示**：恢复成功/失败都有相应提示
- **静默恢复**：不影响用户正常操作

#### 4.4 状态管理优化
- **状态分离**：清晰区分用户偏好档位和设备当前档位
- **持久化存储**：用户偏好档位按设备SN分别存储
- **实时同步**：设备档位变化时实时更新管理器状态

### 5. 使用方法

#### 5.1 保存用户档位
```typescript
// 用户手动设置档位时
this.saveUserPreferredGear(gear);

// 智能调距时
this.saveUserPreferredGear(result);
```

#### 5.2 更新设备档位
```typescript
// 接收到设备档位响应时
this.updateDeviceCurrentGear(currentGearFromDevice);
```

#### 5.3 检查恢复档位
```typescript
// 页面初始化完成后
await this.checkAndRestoreUserGear();
```

### 6. 配置参数

```typescript
const options = {
  maxRetries: 3,        // 最大重试次数
  retryDelay: 2000,     // 重试间隔(ms)
  verifyDelay: 3000,    // 验证延迟(ms)
  showToast: true       // 是否显示提示
};
```

### 7. 错误处理

- **网络异常**：自动重试，超过次数后提示用户
- **设备无响应**：超时后重试，最终失败时提示手动调整
- **档位冲突**：以用户偏好档位为准，强制恢复

### 8. 兼容性

- **向后兼容**：保持原有API不变，新增功能可选使用
- **设备兼容**：支持所有现有设备型号
- **版本兼容**：不依赖特定固件版本

### 9. 测试建议

1. **正常场景**：用户设置档位后退出再进入，验证档位是否正确恢复
2. **异常场景**：设备断开连接时的档位恢复处理
3. **并发场景**：用户操作与档位恢复同时进行的处理
4. **边界场景**：档位值边界（1-15）的处理

### 10. 监控指标

- **恢复成功率**：档位恢复成功的比例
- **重试次数分布**：了解设备响应情况
- **恢复耗时**：档位恢复的平均耗时
- **用户满意度**：档位一致性的用户反馈

## 总结

通过引入专业的用户档位管理器和优化恢复时机，从根本上解决了页面退出时档位恢复不可靠的问题。新方案具有更高的可靠性、更好的用户体验和更清晰的状态管理。
